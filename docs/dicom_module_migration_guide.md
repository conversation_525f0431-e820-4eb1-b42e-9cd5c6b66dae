# DICOM Module Migration Guide

**Migration to Composition-Based Architecture with Internal Dataset Management**

## Overview

This document provides a comprehensive guide for migrating all 44 DICOM modules from the current inheritance-based architecture (inheriting from `pydicom.Dataset`) to a composition-based architecture that manages an internal `pydicom.Dataset` object.

**Implementation Status**: 🚧 MIGRATION REQUIRED | All 44 modules need conversion

**Architecture Change**: `Module IS-A Dataset` → `Module HAS-A Dataset`

## Core Migration Principles

### Design Pattern: Internal Dataset Management
- **Internal Dataset**: All modules maintain a private `self._dataset` attribute
- **Memory Efficiency**: Direct DICOM attribute assignment to `self._dataset.AttributeName`
- **Dataset Generation**: On-demand dataset exposure via `to_dataset()` method
- **No Duplication**: Single source of truth in the internal dataset
- **Clean API**: Minimal properties, focus on builder methods and validation

### Data Handling: Direct Dataset Operations
- **Memory Efficient**: DICOM data stored directly in `self._dataset`
- **Reference Management**: Large data elements stored as references in dataset
- **Live References**: Changes to internal dataset reflected immediately
- **No Property Overhead**: Eliminate most property accessors for cleaner IntelliSense

## Migration Architecture

### New BaseModule Pattern

```python
"""Base Module - Abstract base class for all DICOM modules."""
from abc import ABC, abstractmethod
from typing import Any
import pydicom
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.validators.modules.base_validator import ValidationConfig


class BaseModule(ABC):
    """Abstract base class for all DICOM modules.
    
    Uses composition with internal dataset management rather than
    inheriting from pydicom.Dataset for cleaner separation of concerns.
    """
    
    def __init__(self):
        """Initialize module with internal dataset."""
        self._dataset = pydicom.Dataset()
    
    @classmethod
    @abstractmethod
    def from_required_elements(cls, *args, **kwargs) -> 'BaseModule':
        """Create module instance from all required (Type 1 and Type 2) data elements.
        
        This method must be implemented by each module subclass to define their
        specific required elements according to the DICOM standard.
        
        Returns:
            BaseModule: New module instance with required data elements set
        """
        pass
    
    @abstractmethod
    def with_optional_elements(self, *args, **kwargs) -> 'BaseModule':
        """Add optional (Type 3) data elements to the module instance.
        
        This method must be implemented by each module subclass to define their
        specific optional elements according to the DICOM standard.
        
        Returns:
            BaseModule: Self for method chaining
        """
        pass
    
    def to_dataset(self) -> pydicom.Dataset:
        """Generate DICOM dataset from current module state.
        
        Returns:
            Fresh DICOM dataset containing all module data
        """
        return self._dataset.copy()
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this module instance against DICOM standard.
        
        This method should be overridden by subclasses to provide module-specific
        validation logic using their corresponding validator classes.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        # Default implementation returns no errors/warnings
        # Subclasses should override this with their specific validator
        return ValidationResult()
    
    @property
    def module_name(self) -> str:
        """Get the name of this module class.
        
        Returns:
            str: Module class name
        """
        return self.__class__.__name__
    
    @property
    def has_data(self) -> bool:
        """Check if this module contains any DICOM data elements.
        
        Returns:
            bool: True if module has any data elements
        """
        return len(self._dataset) > 0
    
    def get_element_count(self) -> int:
        """Get the number of DICOM data elements in this module.
        
        Returns:
            int: Number of data elements
        """
        return len(self._dataset)
    
    def __repr__(self) -> str:
        """String representation of the module.
        
        Returns:
            str: Module representation with class name and element count
        """
        return f"{self.module_name}(elements={self.get_element_count()})"
```

### Helper Utilities Module

Create `src/pyrt_dicom/utils/dicom_formatters.py`:

```python
"""DICOM data formatting utilities."""
from datetime import datetime, date
from typing import Any


def format_date_value(date_value: Any) -> str:
    """Format date values to DICOM DA format (YYYYMMDD).
    
    Args:
        date_value: Date value (str, datetime, or date object)
        
    Returns:
        str: Date formatted as YYYYMMDD string
    """
    if isinstance(date_value, (datetime, date)):
        return date_value.strftime("%Y%m%d")
    return str(date_value)


def format_time_value(time_value: Any) -> str:
    """Format time values to DICOM TM format (HHMMSS).
    
    Args:
        time_value: Time value (str or datetime object)
        
    Returns:
        str: Time formatted as HHMMSS string
    """
    if isinstance(time_value, datetime):
        if time_value.microsecond:
            return f"{time_value.strftime('%H%M%S')}.{time_value.microsecond:06d}"
        return time_value.strftime("%H%M%S")
    return str(time_value)


def format_enum_value(enum_value: Any) -> Any:
    """Extract value from enum objects.
    
    Args:
        enum_value: Enum object or other value
        
    Returns:
        Any: Value from enum or original value
    """
    return enum_value.value if hasattr(enum_value, 'value') else enum_value
```

## Migration Pattern Example: ApprovalModule

### Current Implementation (BEFORE)
```python
class ApprovalModule(BaseModule):  # Inherits from Dataset
    def __init__(self):
        super().__init__()  # Calls pydicom.Dataset.__init__
    
    @classmethod
    def from_required_elements(cls, approval_status):
        instance = cls()
        instance.ApprovalStatus = instance._format_enum_value(approval_status)  # Direct assignment
        return instance
    
    @property
    def is_approved(self) -> bool:
        return (hasattr(self, 'ApprovalStatus') and 
                self.ApprovalStatus == ApprovalStatus.APPROVED.value)
```

### New Implementation (AFTER)
```python
"""Approval Module - DICOM PS3.3 C.8.8.16"""
from datetime import datetime, date
from .base_module import BaseModule
from ...enums.approval_enums import ApprovalStatus
from ...validators import ValidationResult
from ...validators.modules.approval_validator import ApprovalValidator
from ...validators.modules.base_validator import ValidationConfig
from ...utils.dicom_formatters import format_date_value, format_time_value, format_enum_value


class ApprovalModule(BaseModule):
    """Approval Module implementation for DICOM PS3.3 C.8.8.16.
    
    Contains attributes that describe the approval status of a DICOM object
    at the time the SOP Instance was created.
    
    Usage:
        # Create with approved status
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240101",
            review_time="120000", 
            reviewer_name="Dr. Smith"
        )
        
        # Create with unapproved status (no review info needed)
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        # Generate dataset for IOD integration
        dataset = approval.to_dataset()
        
        # Validate
        result = approval.validate()
    """
    
    @classmethod
    def from_required_elements(
        cls,
        approval_status: ApprovalStatus | str
    ) -> 'ApprovalModule':
        """Create module with required elements.
        
        Args:
            approval_status: Approval status at the time the SOP Instance was created
            
        Returns:
            ApprovalModule: New module instance with required data elements set
        """
        instance = cls()
        instance._dataset.ApprovalStatus = format_enum_value(approval_status)
        return instance
    
    def with_review_information(
        self,
        review_date: str | date | datetime,
        review_time: str | datetime,
        reviewer_name: str
    ) -> 'ApprovalModule':
        """Add review information (Type 2C).
        
        Required if Approval Status (300E,0002) is APPROVED or REJECTED.
        Should not be present if Approval Status is UNAPPROVED.
        
        Args:
            review_date: Date on which object was reviewed (300E,0004) - DICOM DA format
            review_time: Time at which object was reviewed (300E,0005) - DICOM TM format  
            reviewer_name: Name of person who reviewed object (300E,0008) - DICOM PN format
            
        Returns:
            ApprovalModule: Self for method chaining
            
        Raises:
            ValueError: If trying to add review information for UNAPPROVED status
        """
        # Validate conditional requirement before setting
        if hasattr(self._dataset, 'ApprovalStatus'):
            approval_status = str(self._dataset.ApprovalStatus)
            if approval_status == ApprovalStatus.UNAPPROVED.value:
                raise ValueError(
                    "Review information should not be provided for UNAPPROVED status. "
                    "Type 2C elements ReviewDate, ReviewTime, and ReviewerName are only "
                    "required when ApprovalStatus is APPROVED or REJECTED."
                )
        
        self._dataset.ReviewDate = format_date_value(review_date)
        self._dataset.ReviewTime = format_time_value(review_time)
        self._dataset.ReviewerName = reviewer_name
        return self
    
    def with_optional_elements(self) -> 'ApprovalModule':
        """Add optional (Type 3) elements.
        
        Note: The core DICOM PS3.3 C.8.8.16 Approval Module contains only:
        - Type 1: ApprovalStatus (required)
        - Type 2C: ReviewDate, ReviewTime, ReviewerName (conditional)
        
        There are no Type 3 (optional) elements defined in this module.
        This method is provided for API consistency with other modules.
        
        Returns:
            ApprovalModule: Self for method chaining
        """
        # No Type 3 elements defined in DICOM PS3.3 C.8.8.16 Approval Module
        return self
    
    @property
    def is_approved(self) -> bool:
        """Check if object is approved.
        
        Returns:
            bool: True if approval status is APPROVED
        """
        return (hasattr(self._dataset, 'ApprovalStatus') and 
                self._dataset.ApprovalStatus == ApprovalStatus.APPROVED.value)
    
    @property
    def is_rejected(self) -> bool:
        """Check if object is rejected.
        
        Returns:
            bool: True if approval status is REJECTED
        """
        return (hasattr(self._dataset, 'ApprovalStatus') and 
                self._dataset.ApprovalStatus == ApprovalStatus.REJECTED.value)
    
    @property
    def is_unapproved(self) -> bool:
        """Check if object is unapproved.
        
        Returns:
            bool: True if approval status is UNAPPROVED
        """
        return (hasattr(self._dataset, 'ApprovalStatus') and 
                self._dataset.ApprovalStatus == ApprovalStatus.UNAPPROVED.value)
    
    @property
    def requires_review_information(self) -> bool:
        """Check if review information is required.
        
        Returns:
            bool: True if approval status requires review information
        """
        return self.is_approved or self.is_rejected
    
    @property
    def has_review_information(self) -> bool:
        """Check if review information is present.
        
        Returns:
            bool: True if all review information fields are present
        """
        return (hasattr(self._dataset, 'ReviewDate') and 
                hasattr(self._dataset, 'ReviewTime') and 
                hasattr(self._dataset, 'ReviewerName'))
    
    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured.
        
        Returns:
            bool: True if approval status is present
        """
        return hasattr(self._dataset, 'ApprovalStatus')
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Approval Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return ApprovalValidator.validate(self._dataset, config)
```

## Migration Checklist: All 44 Modules

### Phase 1: Infrastructure Setup ✅ REQUIRED FIRST
- [ ] **Create utility module**: `src/pyrt_dicom/utils/dicom_formatters.py`
- [ ] **Update BaseModule**: Migrate to internal dataset management
- [ ] **Update BaseIOD**: Ensure `to_dataset()` integration works with modules

### Phase 2: Module Migration (Alphabetical Order)

| # | Module Name | Status | Notes |
|---|-------------|--------|-------|
| 1 | **approval_module.py** | ✅ COMPLETE | Migration validated with 22/22 tests passing |
| 2 | **cine_module.py** | ✅ COMPLETE | Migration validated with 13/13 tests passing |
| 3 | **clinical_trial_series_module.py** | ✅ COMPLETE | Migration validated with 13/13 tests passing |
| 4 | **clinical_trial_study_module.py** | ❌ TODO | Clinical trial study info |
| 5 | **clinical_trial_subject_module.py** | ❌ TODO | Clinical trial subject info |
| 6 | **common_instance_reference_module.py** | ❌ TODO | Cross-reference module |
| 7 | **contrast_bolus_module.py** | ❌ TODO | Contrast agent information |
| 8 | **ct_image_module.py** | ❌ TODO | CT-specific imaging parameters |
| 9 | **device_module.py** | ❌ TODO | Device and equipment info |
| 10 | **enhanced_patient_orientation_module.py** | ❌ TODO | Patient positioning |
| 11 | **frame_extraction_module.py** | ❌ TODO | Frame-level retrieval |
| 12 | **frame_of_reference_module.py** | ❌ TODO | Spatial coordinate system |
| 13 | **general_acquisition_module.py** | ❌ TODO | Acquisition parameters |
| 14 | **general_equipment_module.py** | ❌ TODO | Equipment information |
| 15 | **general_image_module.py** | ❌ TODO | General image attributes |
| 16 | **general_reference_module.py** | ❌ TODO | General references |
| 17 | **general_series_module.py** | ❌ TODO | Series-level metadata |
| 18 | **general_study_module.py** | ❌ TODO | Study-level metadata |
| 19 | **image_pixel_module.py** | ❌ TODO | Pixel data attributes |
| 20 | **image_plane_module.py** | ❌ TODO | Spatial image characteristics |
| 21 | **modality_lut_module.py** | ❌ TODO | Modality LUT transformation |
| 22 | **multi_energy_ct_image_module.py** | ❌ TODO | Multi-energy CT parameters |
| 23 | **multi_frame_module.py** | ❌ TODO | Multi-frame attributes |
| 24 | **overlay_plane_module.py** | ❌ TODO | Overlay graphics |
| 25 | **patient_module.py** | ❌ TODO | Patient demographics |
| 26 | **patient_study_module.py** | ❌ TODO | Patient study information |
| 27 | **roi_contour_module.py** | ❌ TODO | ROI contour geometry |
| 28 | **rt_beams_module.py** | ❌ TODO | RT beam definitions |
| 29 | **rt_brachy_application_setups_module.py** | ❌ TODO | Brachytherapy setups |
| 30 | **rt_dose_module.py** | ❌ TODO | RT dose distribution |
| 31 | **rt_dvh_module.py** | ❌ TODO | Dose volume histograms |
| 32 | **rt_fraction_scheme_module.py** | ❌ TODO | Fractionation schedules |
| 33 | **rt_general_plan_module.py** | ❌ TODO | General plan information |
| 34 | **rt_image_module.py** | ❌ TODO | RT imaging parameters |
| 35 | **rt_patient_setup_module.py** | ❌ TODO | Patient setup instructions |
| 36 | **rt_prescription_module.py** | ❌ TODO | Dose prescriptions |
| 37 | **rt_roi_observations_module.py** | ❌ TODO | ROI observations |
| 38 | **rt_series_module.py** | ❌ TODO | RT series metadata |
| 39 | **rt_tolerance_tables_module.py** | ❌ TODO | Machine tolerance tables |
| 40 | **sop_common_module.py** | ❌ TODO | SOP common attributes |
| 41 | **specimen_module.py** | ❌ TODO | Specimen information |
| 42 | **structure_set_module.py** | ❌ TODO | Structure set definitions |
| 43 | **synchronization_module.py** | ❌ TODO | Synchronization parameters |
| 44 | **voi_lut_module.py** | ❌ TODO | VOI LUT transformation |

### Phase 3: Testing Updates (Parallel with Migration)

For each migrated module, update the corresponding pytest file:

| # | Test File | Status | Migration Notes |
|---|-----------|--------|-----------------|
| 1 | **test_approval_module.py** | ✅ COMPLETE | Updated with `to_dataset()` method tests and integration test |
| 2 | **test_cine_module.py** | ✅ COMPLETE | Updated with `to_dataset()` method tests |
| 3 | **test_clinical_trial_series_module.py** | ✅ COMPLETE | Updated with `to_dataset()` method tests |
| 4-44 | **test_[module]_module.py** | ❌ TODO | Same pattern for all modules |

## Step-by-Step Migration Process

### For Each Module:

#### 1. **Update Class Declaration**
```python
# BEFORE
class ModuleName(BaseModule):
    def __init__(self):
        super().__init__()  # Calls Dataset.__init__

# AFTER
class ModuleName(BaseModule):
    # __init__ inherited from BaseModule - creates self._dataset
```

#### 2. **Update Builder Methods**
```python
# BEFORE
@classmethod
def from_required_elements(cls, param1, param2):
    instance = cls()
    instance.Attribute1 = value1
    instance.Attribute2 = instance._format_enum_value(param2)
    return instance

# AFTER
@classmethod  
def from_required_elements(cls, param1, param2):
    instance = cls()
    instance._dataset.Attribute1 = value1
    instance._dataset.Attribute2 = format_enum_value(param2)
    return instance
```

#### 3. **Update Helper Method Calls**
```python
# BEFORE
self.ReviewDate = self._format_date_value(review_date)
self.ReviewTime = self._format_time_value(review_time)

# AFTER
from ...utils.dicom_formatters import format_date_value, format_time_value
self._dataset.ReviewDate = format_date_value(review_date)
self._dataset.ReviewTime = format_time_value(review_time)
```

#### 4. **Update Property Methods**
```python
# BEFORE
@property
def is_approved(self) -> bool:
    return (hasattr(self, 'ApprovalStatus') and 
            self.ApprovalStatus == ApprovalStatus.APPROVED.value)

# AFTER
@property
def is_approved(self) -> bool:
    return (hasattr(self._dataset, 'ApprovalStatus') and 
            self._dataset.ApprovalStatus == ApprovalStatus.APPROVED.value)
```

#### 5. **Update Validation Call**
```python
# BEFORE
def validate(self, config=None) -> ValidationResult:
    return ModuleValidator.validate(self, config)

# AFTER
def validate(self, config=None) -> ValidationResult:
    return ModuleValidator.validate(self._dataset, config)
```

#### 6. **Update Test Files**
```python
# BEFORE
def test_module_creation():
    module = ModuleName.from_required_elements(...)
    assert module.AttributeName == expected_value

# AFTER
def test_module_creation():
    module = ModuleName.from_required_elements(...)
    dataset = module.to_dataset()
    assert dataset.AttributeName == expected_value
    
def test_dataset_generation():
    module = ModuleName.from_required_elements(...)
    dataset = module.to_dataset()
    assert isinstance(dataset, pydicom.Dataset)
    assert len(dataset) > 0
```

## Migration Benefits

### 1. **Clean Architecture**
- Clear separation between business logic (module) and data storage (dataset)
- No Dataset inheritance complexity
- Focused API with minimal property pollution

### 2. **Memory Efficiency**
- Direct dataset operations: `self._dataset.AttributeName = value`
- Large data stored by reference in dataset
- No duplication between structured state and dataset representation

### 3. **IOD Integration**
- Seamless integration with `BaseIOD.to_dataset()` method
- Live references maintained through internal dataset
- Consistent `to_dataset()` pattern across modules and IODs

### 4. **Validation Independence**
- Validators work with any pydicom dataset
- Memory-efficient validation using direct dataset references
- No coupling between validators and module implementation

## Migration Validation Steps

### For Each Module:
1. **API Compatibility**: Ensure all builder methods work identically
2. **Dataset Generation**: Verify `to_dataset()` produces correct DICOM datasets
3. **Property Accuracy**: Test all boolean properties return correct values
4. **Validation Integration**: Confirm validators work with internal datasets
5. **IOD Integration**: Test module works correctly within IOD `to_dataset()` calls

### System-Wide Testing:
1. **IOD Generation**: Verify all IODs can generate valid DICOM files
2. **Memory Usage**: Confirm no memory regressions with large datasets
3. **Performance**: Validate dataset generation performance meets targets
4. **DICOM Compliance**: Test generated files pass external validation tools

## Timeline and Priorities

### Immediate Priority (Phase 1): Infrastructure
- **Week 1**: Create utility modules and update BaseModule
- **Week 1**: Update BaseIOD integration and test framework

### Migration Execution (Phase 2): Module Conversion  
- **Week 2-3**: Migrate modules 1-15 (alphabetical order)
- **Week 4-5**: Migrate modules 16-30 (alphabetical order)
- **Week 6**: Migrate modules 31-44 (alphabetical order)
- **Week 7**: Integration testing and DICOM compliance validation

### Quality Assurance (Phase 3): Validation
- **Week 8**: System-wide testing and performance validation
- **Week 9**: Documentation updates and migration verification
- **Week 10**: Final validation and migration completion

## Success Criteria

### Module-Level Success:
- ✅ All 44 modules migrated to internal dataset architecture
- ✅ All builder methods (`from_required_elements`, `with_optional_elements`) work correctly
- ✅ All property methods access `self._dataset` attributes correctly
- ✅ All validation calls use `self._dataset` for memory efficiency

### System-Level Success:
- ✅ All IODs generate valid DICOM datasets using migrated modules
- ✅ Memory usage remains efficient with large datasets (CT volumes, dose grids)
- ✅ Generated DICOM files pass external validation tools
- ✅ No performance regressions in dataset generation or file I/O operations

### Testing Success:
- ✅ All 44 module test files updated and passing
- ✅ Integration tests demonstrate seamless IOD workflow functionality
- ✅ Performance benchmarks confirm efficiency targets are met
- ✅ DICOM compliance testing validates standard adherence

This migration guide provides a systematic approach to converting all DICOM modules to the new composition-based architecture while maintaining full functionality and improving memory efficiency through direct internal dataset management.

---

*Document created: 2025-08-22*  
*Migration scope: 44 DICOM modules + BaseModule + utility functions*  
*Architecture change: Inheritance → Composition with internal dataset management*